"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import SearchChips, { SearchChip } from '@/components/SearchChips';
import { useSearchChips } from '@/hooks/useSearchChips';
import { SearchCondition } from '@/lib/api';

export default function TestSearchChipsPage() {
  // 模拟搜索状态
  const [filters, setFilters] = useState<Record<string, unknown>>({
    allFields: '医疗器械',
    productName: '心脏起搏器',
    companyName: '美敦力',
    status: '有效'
  });

  const [advancedConditions, setAdvancedConditions] = useState<SearchCondition[]>([
    {
      id: '1',
      field: 'approvalDate',
      operator: 'between',
      value: { from: '2020-01-01', to: '2023-12-31' },
      logic: 'AND'
    },
    {
      id: '2',
      field: 'riskLevel',
      operator: 'in',
      value: ['III类', 'II类'],
      logic: 'AND'
    }
  ]);

  const [sortBy, setSortBy] = useState<string>('approvalDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // 字段配置
  const fieldConfigs = [
    { fieldName: 'allFields', displayName: '综合搜索' },
    { fieldName: 'productName', displayName: '产品名称' },
    { fieldName: 'companyName', displayName: '公司名称' },
    { fieldName: 'status', displayName: '状态' },
    { fieldName: 'approvalDate', displayName: '批准日期' },
    { fieldName: 'riskLevel', displayName: '风险等级' },
  ];

  // 使用搜索标签 Hook
  const searchChips = useSearchChips({
    filters,
    advancedConditions,
    sortBy,
    sortOrder,
    fieldConfigs,
    onFilterChange: (key: string, value: unknown) => {
      setFilters(prev => {
        const newFilters = { ...prev };
        if (value === undefined || value === null || value === '') {
          delete newFilters[key];
        } else {
          newFilters[key] = value;
        }
        return newFilters;
      });
    },
    onAdvancedConditionRemove: (conditionId: string) => {
      setAdvancedConditions(prev => prev.filter(c => c.id !== conditionId));
    },
    onSortChange: (newSortBy?: string, newSortOrder?: 'asc' | 'desc') => {
      setSortBy(newSortBy || '');
      setSortOrder(newSortOrder || 'desc');
    },
    onClearAll: () => {
      setFilters({});
      setAdvancedConditions([]);
      setSortBy('');
      setSortOrder('desc');
    }
  });

  // 添加新的过滤器
  const addFilter = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 添加新的高级搜索条件
  const addAdvancedCondition = () => {
    const newCondition: SearchCondition = {
      id: Date.now().toString(),
      field: 'category',
      operator: 'contains',
      value: '植入器械',
      logic: 'AND'
    };
    setAdvancedConditions(prev => [...prev, newCondition]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>搜索标签组件测试</CardTitle>
            <p className="text-gray-600">
              测试可移除标签的搜索条件功能 (Removable Filter Tags / Search Chips)
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 搜索标签展示 */}
            <div>
              <Label className="text-base font-medium mb-3 block">当前搜索条件:</Label>
              <div className="border rounded-lg">
                <SearchChips
                  chips={searchChips.chips}
                  onRemoveChip={searchChips.handleRemoveChip}
                  onClearAll={searchChips.handleClearAll}
                  showClearAll={true}
                  maxDisplay={10}
                  compact={false}
                />
                {!searchChips.hasActiveFilters && (
                  <div className="p-4 text-gray-500 text-center">
                    暂无搜索条件
                  </div>
                )}
              </div>
            </div>

            {/* 紧凑模式展示 */}
            <div>
              <Label className="text-base font-medium mb-3 block">紧凑模式:</Label>
              <div className="border rounded-lg">
                <SearchChips
                  chips={searchChips.chips}
                  onRemoveChip={searchChips.handleRemoveChip}
                  onClearAll={searchChips.handleClearAll}
                  showClearAll={true}
                  maxDisplay={5}
                  compact={true}
                />
                {!searchChips.hasActiveFilters && (
                  <div className="p-2 text-gray-500 text-center text-sm">
                    暂无搜索条件
                  </div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                onClick={() => addFilter('deviceClass', 'III类')}
                variant="outline"
              >
                添加器械分类
              </Button>
              <Button 
                onClick={() => addFilter('manufacturer', '强生')}
                variant="outline"
              >
                添加生产厂商
              </Button>
              <Button 
                onClick={addAdvancedCondition}
                variant="outline"
              >
                添加高级条件
              </Button>
              <Button 
                onClick={() => {
                  setSortBy('productName');
                  setSortOrder('asc');
                }}
                variant="outline"
              >
                设置排序
              </Button>
            </div>

            {/* 状态信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <Card>
                <CardContent className="p-4">
                  <div className="font-medium text-gray-700">过滤器数量</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {Object.keys(filters).length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="font-medium text-gray-700">高级条件数量</div>
                  <div className="text-2xl font-bold text-green-600">
                    {advancedConditions.length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="font-medium text-gray-700">总标签数量</div>
                  <div className="text-2xl font-bold text-purple-600">
                    {searchChips.chipCount}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 当前状态详情 */}
            <div className="space-y-4">
              <div>
                <Label className="font-medium">当前过滤器:</Label>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(filters, null, 2)}
                </pre>
              </div>
              <div>
                <Label className="font-medium">高级搜索条件:</Label>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(advancedConditions, null, 2)}
                </pre>
              </div>
              <div>
                <Label className="font-medium">排序设置:</Label>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify({ sortBy, sortOrder }, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
