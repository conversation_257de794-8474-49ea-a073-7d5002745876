import React from 'react';
import GlobalSearchWithChips from '@/components/GlobalSearchWithChips';

export default function DemoSearchChipsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-12">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            医疗器械数据库搜索
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            使用智能搜索标签功能，轻松管理和重用您的搜索条件
          </p>
        </div>

        {/* 搜索组件 */}
        <GlobalSearchWithChips className="max-w-4xl mx-auto" />

        {/* 功能特性介绍 */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            搜索标签功能特性
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">智能搜索历史</h3>
              <p className="text-gray-600">
                自动保存搜索历史，支持快速重新搜索，提高搜索效率
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">可移除标签</h3>
              <p className="text-gray-600">
                点击标签上的 × 按钮即可移除单个搜索条件，操作简单直观
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">用户体验优化</h3>
              <p className="text-gray-600">
                紧凑模式、最大显示数量限制、清除所有等功能提升使用体验
              </p>
            </div>
          </div>
        </div>

        {/* 使用场景 */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            适用场景
          </h2>
          
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">全站综合搜索</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• 首页搜索框的搜索历史管理</li>
                  <li>• 跨数据库的搜索条件展示</li>
                  <li>• 快速重复搜索常用关键词</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">数据库页面搜索</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• 显示当前应用的所有筛选条件</li>
                  <li>• 支持移除单个筛选条件</li>
                  <li>• 高级搜索条件的可视化展示</li>
                  <li>• 排序条件的标签化显示</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 技术实现 */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            技术实现
          </h2>
          
          <div className="bg-gray-900 rounded-lg p-6 text-white">
            <h3 className="text-lg font-semibold mb-4">核心组件</h3>
            <div className="grid md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-blue-300 mb-2">SearchChips 组件</h4>
                <ul className="space-y-1 text-gray-300">
                  <li>• 可配置的标签显示样式</li>
                  <li>• 支持不同类型的搜索条件</li>
                  <li>• 紧凑模式和普通模式</li>
                  <li>• 最大显示数量限制</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-green-300 mb-2">useSearchChips Hook</h4>
                <ul className="space-y-1 text-gray-300">
                  <li>• 统一的搜索条件管理</li>
                  <li>• 自动转换不同类型的条件</li>
                  <li>• 提供移除和清除操作</li>
                  <li>• 支持自定义字段配置</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
