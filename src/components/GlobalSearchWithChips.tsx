"use client";

import React, { useState, useCallback } from 'react';
import { Search, ExternalLink } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import SearchChips from '@/components/SearchChips';
import { useSimpleSearchChips } from '@/hooks/useSearchChips';
import { useGlobalSearch } from '@/hooks/use-global-search';
import Link from 'next/link';

interface GlobalSearchWithChipsProps {
  className?: string;
}

export default function GlobalSearchWithChips({ className }: GlobalSearchWithChipsProps) {
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [currentQuery, setCurrentQuery] = useState('');
  
  // 使用全局搜索Hook
  const { results, isSearching, performSearch } = useGlobalSearch();

  // 将搜索历史转换为过滤器格式
  const historyFilters = React.useMemo(() => {
    const filters: Record<string, unknown> = {};
    searchHistory.forEach((query, index) => {
      filters[`history_${index}`] = query;
    });
    return filters;
  }, [searchHistory]);

  // 使用搜索标签Hook管理搜索历史
  const searchChips = useSimpleSearchChips(
    historyFilters,
    (key: string, value: unknown) => {
      if (value === undefined || value === null) {
        // 移除搜索历史项
        const index = parseInt(key.replace('history_', ''));
        setSearchHistory(prev => prev.filter((_, i) => i !== index));
      }
    },
    searchHistory.map((query, index) => ({
      fieldName: `history_${index}`,
      displayName: `搜索: ${query}`
    }))
  );

  // 执行搜索
  const handleSearch = useCallback(async () => {
    if (!currentQuery.trim()) return;
    
    // 添加到搜索历史
    setSearchHistory(prev => {
      const newHistory = [currentQuery, ...prev.filter(q => q !== currentQuery)];
      return newHistory.slice(0, 5); // 最多保留5个搜索历史
    });
    
    // 执行搜索
    await performSearch(currentQuery);
  }, [currentQuery, performSearch]);

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    }
  };

  // 点击搜索历史标签重新搜索
  const handleChipClick = (chipId: string) => {
    const index = parseInt(chipId.replace('filter_history_', ''));
    const query = searchHistory[index];
    if (query) {
      setCurrentQuery(query);
      performSearch(query);
    }
  };

  // 清除所有搜索历史
  const handleClearHistory = () => {
    setSearchHistory([]);
  };

  return (
    <div className={className}>
      {/* 搜索框 */}
      <div className="flex items-center w-full max-w-2xl mx-auto mb-6">
        <div className="relative flex-1">
          <Input
            type="text"
            placeholder="输入产品名称、公司名称、申请号、注册号或标题进行搜索"
            className="h-12 text-sm md:text-base pr-16 border-r-0 rounded-r-none"
            value={currentQuery}
            onChange={(e) => setCurrentQuery(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        </div>
        <Button
          className="h-12 px-4 md:px-8 rounded-l-none bg-blue-600 hover:bg-blue-700"
          disabled={!currentQuery.trim() || isSearching}
          onClick={handleSearch}
        >
          {isSearching ? '搜索中...' : '搜索'}
        </Button>
      </div>

      {/* 搜索历史标签 */}
      {searchChips.hasActiveFilters && (
        <div className="mb-6">
          <SearchChips
            chips={searchChips.chips.map(chip => ({
              ...chip,
              displayValue: chip.displayValue.replace('搜索: ', ''),
              color: 'outline' as const
            }))}
            onRemoveChip={searchChips.handleRemoveChip}
            onClearAll={handleClearHistory}
            showClearAll={true}
            maxDisplay={5}
            compact={true}
            className="bg-white border border-gray-200 rounded-lg"
          />
        </div>
      )}

      {/* 搜索结果 */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            搜索结果 "{currentQuery}"
          </h3>
          
          <div className="grid gap-4 md:grid-cols-2">
            {results.map((result) => (
              <Card key={result.database} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span>{result.database}</span>
                    <span className="text-sm font-normal text-gray-500">
                      {result.count} 条结果
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {result.count > 0 ? (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        找到 {result.count} 条相关记录
                      </div>
                      <Link
                        href={`/data/list/${result.database}?allFields=${encodeURIComponent(currentQuery)}`}
                        className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        查看详细结果
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      未找到相关记录
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* 无结果提示 */}
      {results.length === 0 && currentQuery && !isSearching && (
        <div className="text-center py-8">
          <p className="text-gray-500">未找到 "{currentQuery}" 的相关结果</p>
          <p className="text-sm text-gray-400 mt-1">
            请尝试使用不同的关键词或检查拼写
          </p>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-gray-50 rounded-md">
        <h4 className="font-medium text-gray-900 mb-2">搜索标签功能说明</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• <strong>搜索历史</strong>：自动保存最近5次搜索，点击标签可快速重新搜索</li>
          <li>• <strong>移除标签</strong>：点击标签上的 × 按钮可移除单个搜索历史</li>
          <li>• <strong>清除所有</strong>：点击"Clear All"按钮可清除所有搜索历史</li>
          <li>• <strong>智能去重</strong>：相同的搜索词不会重复添加到历史中</li>
        </ul>
      </div>
    </div>
  );
}
